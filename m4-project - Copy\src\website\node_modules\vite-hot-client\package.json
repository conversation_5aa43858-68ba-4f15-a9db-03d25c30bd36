{"name": "vite-hot-client", "type": "module", "version": "2.0.4", "description": "Get Vite's import.meta.hot at runtime.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-hot-client#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vite-hot-client.git"}, "bugs": {"url": "https://github.com/antfu/vite-hot-client/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "peerDependencies": {"vite": "^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}, "devDependencies": {"@antfu/eslint-config": "^3.12.2", "@antfu/ni": "^23.2.0", "@babel/types": "^7.26.3", "@types/node": "^22.10.5", "bumpp": "^9.9.3", "eslint": "^9.17.0", "pnpm": "^9.15.3", "rimraf": "^6.0.1", "tsx": "^4.19.2", "typescript": "^5.7.3", "unbuild": "^3.2.0", "vite": "^6.0.7", "vitest": "^2.1.8"}, "scripts": {"build": "rimraf dist && unbuild && tsx scripts/patch-types.ts", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "typecheck": "tsc --noEmit"}}