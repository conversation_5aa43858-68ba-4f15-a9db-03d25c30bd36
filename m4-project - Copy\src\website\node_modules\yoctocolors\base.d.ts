export type Format = (string: string) => string;

export const reset: Format;
export const bold: Format;
export const dim: Format;
export const italic: Format;
export const underline: Format;
export const overline: Format;
export const inverse: Format;
export const hidden: Format;
export const strikethrough: Format;

export const black: Format;
export const red: Format;
export const green: Format;
export const yellow: Format;
export const blue: Format;
export const magenta: Format;
export const cyan: Format;
export const white: Format;
export const gray: Format;

export const bgBlack: Format;
export const bgRed: Format;
export const bgGreen: Format;
export const bgYellow: Format;
export const bgBlue: Format;
export const bgMagenta: Format;
export const bgCyan: Format;
export const bgWhite: Format;
export const bgGray: Format;

export const redBright: Format;
export const greenBright: Format;
export const yellowBright: Format;
export const blueBright: Format;
export const magentaBright: Format;
export const cyanBright: Format;
export const whiteBright: Format;

export const bgRedBright: Format;
export const bgGreenBright: Format;
export const bgYellowBright: Format;
export const bgBlueBright: Format;
export const bgMagentaBright: Format;
export const bgCyanBright: Format;
export const bgWhiteBright: Format;
