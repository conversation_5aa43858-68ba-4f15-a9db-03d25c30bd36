<script setup>
import { ref, reactive, inject, computed } from 'vue'

// Define props for the component
const props = defineProps({
  title: {
    type: String,
    default: 'Quadrant Title'
  },
  addButtonText: {
    type: String,
    default: '+Add Item'
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['classes', 'students', 'documents', 'statistics', 'default'].includes(value)
  }
})

// Inject global state
const selectedClass = inject('selectedClass')
const selectClass = inject('selectClass')

// Reactive data arrays for different quadrant types
const classesData = ref([])
const studentsData = ref([]) // This will store students with classId
const documentsData = ref([])

// Statistics data with reactive subjects and metrics
const statisticsData = reactive({
  subjects: ['math', 'english', 'science'],
  classAverage: [8.2, 7.5, 8.8],
  reachedNorm: ['yes', 'no', 'yes'],
  passingPerc: ['85%', '72%', '91%']
})

// Form visibility states
const showClassForm = ref(false)
const showStudentForm = ref(false)
const showDocumentForm = ref(false)

// Form data
const newClass = reactive({
  name: '',
  teacherFirstName: '',
  teacherLastName: ''
})

const newStudent = ref('')

// Computed property to filter students by selected class
const filteredStudents = computed(() => {
  if (!selectedClass.value) return []
  return studentsData.value.filter(student => student.classId === selectedClass.value.id)
})

// Computed property to filter documents by selected class
const filteredDocuments = computed(() => {
  if (!selectedClass.value) return []
  return documentsData.value.filter(document => document.classId === selectedClass.value.id)
})

// Computed property to filter statistics by selected class
const filteredStatistics = computed(() => {
  if (!selectedClass.value) return { subjects: [], classAverage: [], reachedNorm: [], passingPerc: [] }
  // For now, return the statistics data if it matches the selected class
  // In a real app, you'd filter based on classId
  return statisticsData
})
const newDocument = reactive({
  title: '',
  editor: ''
})

// Add functions
const addClass = () => {
  if (newClass.name && newClass.teacherFirstName && newClass.teacherLastName) {
    const fullTeacherName = `${newClass.teacherFirstName} ${newClass.teacherLastName}`
    classesData.value.push({
      id: Date.now(),
      name: newClass.name,
      teacher: fullTeacherName
    })
    newClass.name = ''
    newClass.teacherFirstName = ''
    newClass.teacherLastName = ''
    showClassForm.value = false
  }
}

const addStudent = () => {
  if (newStudent.value.trim() && selectedClass.value) {
    studentsData.value.push({
      id: Date.now(),
      name: newStudent.value.trim(),
      classId: selectedClass.value.id
    })
    newStudent.value = ''
    showStudentForm.value = false
  }
}

const addDocument = () => {
  if (newDocument.title && newDocument.editor && selectedClass.value) {
    const currentDate = new Date().toLocaleDateString('en-GB')
    documentsData.value.push({
      id: Date.now(),
      title: newDocument.title,
      lastEdited: currentDate,
      editor: newDocument.editor,
      classId: selectedClass.value.id
    })
    newDocument.title = ''
    newDocument.editor = ''
    showDocumentForm.value = false
  }
}

// Handle add button clicks
const handleAddClick = () => {
  console.log('Add button clicked, type:', props.type) // Debug log
  switch (props.type) {
    case 'classes':
      console.log('Showing class form') // Debug log
      showClassForm.value = true
      break
    case 'students':
      if (!selectedClass.value) {
        alert('Please select a class first before adding students.')
        return
      }
      showStudentForm.value = true
      break
    case 'documents':
      if (!selectedClass.value) {
        alert('Please select a class first before adding documents.')
        return
      }
      showDocumentForm.value = true
      break
  }
}

// Handle class selection
const handleClassSelect = (classItem) => {
  selectClass(classItem)
}

// Cancel forms
const cancelForm = () => {
  showClassForm.value = false
  showStudentForm.value = false
  showDocumentForm.value = false
}

// Get Bootstrap color class for each quadrant type - all violet
const getCardColor = (type) => {
  // Using a custom violet color for all headers
  return 'violet'
}
</script>

<template>
  <div class="card h-100" :class="`quadrant-${type}`">
    <div class="card-header d-flex justify-content-between align-items-center bg-violet">
      <h2 class="card-title h5 mb-0 text-white">{{ title }}</h2>
      <button v-if="addButtonText && type !== 'statistics'" @click="handleAddClick" class="btn btn-purple btn-sm">{{ addButtonText }}</button>
    </div>
    <div class="card-body p-3">

      <!-- Classes Content -->
      <div v-if="type === 'classes'" class="classes-content">
        <!-- Add Class Form -->
        <div v-if="showClassForm" class="card mb-3">
          <div class="card-body">
            <h5 class="card-title">Add New Class</h5>
            <div class="mb-3">
              <input v-model="newClass.name" placeholder="Class name (e.g., class 5)" class="form-control" />
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <input v-model="newClass.teacherFirstName" placeholder="Teacher first name" class="form-control" />
              </div>
              <div class="col-md-6 mb-3">
                <input v-model="newClass.teacherLastName" placeholder="Teacher last name" class="form-control" />
              </div>
            </div>
            <div class="d-flex gap-2">
              <button @click="addClass" class="btn btn-purple">Add Class</button>
              <button @click="cancelForm" class="btn btn-secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Classes List -->
        <div v-if="classesData.length > 0" class="row g-2">
          <div
            v-for="classItem in classesData"
            :key="classItem.id"
            class="col-md-6"
          >
            <div
              class="card class-item"
              :class="{ 'border-primary bg-primary bg-opacity-10': selectedClass && selectedClass.id === classItem.id }"
              @click="handleClassSelect(classItem)"
              style="cursor: pointer;"
            >
              <div class="card-body p-2">
                <h6 class="card-title mb-1">{{ classItem.name }}</h6>
                <p class="card-text small text-muted mb-0">{{ classItem.teacher }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="classesData.length === 0 && !showClassForm" class="text-center text-muted py-4">
          <p class="mb-0">No classes added yet. Click "+Add Class" to get started.</p>
        </div>
      </div>

      <!-- Students Content -->
      <div v-else-if="type === 'students'" class="students-content">
        <!-- Add Student Form -->
        <div v-if="showStudentForm" class="card mb-3">
          <div class="card-body">
            <h5 class="card-title">Add New Student</h5>
            <div class="mb-3">
              <input v-model="newStudent" placeholder="Student name (e.g., John Doe)" class="form-control" />
            </div>
            <div class="d-flex gap-2">
              <button @click="addStudent" class="btn btn-purple">Add Student</button>
              <button @click="cancelForm" class="btn btn-secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Students List -->
        <div v-if="filteredStudents.length > 0" class="row g-2">
          <div v-for="student in filteredStudents" :key="student.id" class="col-md-6">
            <div class="card">
              <div class="card-body p-2 text-center">
                <span class="small">{{ student.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!selectedClass && !showStudentForm" class="text-center text-muted py-4">
          <p class="mb-0">Please select a class first to view and add students.</p>
        </div>

        <div v-else-if="selectedClass && filteredStudents.length === 0 && !showStudentForm" class="text-center text-muted py-4">
          <p class="mb-0">No students in {{ selectedClass.name }} yet. Click "+Add Student" to get started.</p>
        </div>
      </div>

      <!-- Documents Content -->
      <div v-else-if="type === 'documents'" class="documents-content">
        <!-- Add Document Form -->
        <div v-if="showDocumentForm" class="card mb-3">
          <div class="card-body">
            <h5 class="card-title">Add New Document</h5>
            <div class="mb-3">
              <input v-model="newDocument.title" placeholder="Document title (e.g., May 2025)" class="form-control" />
            </div>
            <div class="mb-3">
              <input v-model="newDocument.editor" placeholder="Editor name (e.g., Mr. Smith)" class="form-control" />
            </div>
            <div class="d-flex gap-2">
              <button @click="addDocument" class="btn btn-purple">Add Document</button>
              <button @click="cancelForm" class="btn btn-secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Documents List -->
        <div v-if="filteredDocuments.length > 0" class="list-group">
          <div v-for="doc in filteredDocuments" :key="doc.id" class="list-group-item">
            <div class="d-flex w-100 justify-content-between">
              <h6 class="mb-1">{{ doc.title }}</h6>
              <small>{{ doc.lastEdited }}</small>
            </div>
            <small class="text-muted">Edited by {{ doc.editor }}</small>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!selectedClass && !showDocumentForm" class="text-center text-muted py-4">
          <p class="mb-0">Please select a class first to view and add documents.</p>
        </div>

        <div v-else-if="selectedClass && filteredDocuments.length === 0 && !showDocumentForm" class="text-center text-muted py-4">
          <p class="mb-0">No documents for {{ selectedClass.name }} yet. Click "+New Document" to get started.</p>
        </div>
      </div>

      <!-- Statistics Content -->
      <div v-else-if="type === 'statistics'" class="statistics-content">
        <!-- Statistics Table -->
        <div v-if="selectedClass && filteredStatistics.subjects.length > 0">
          <table class="table table-striped table-sm">
            <thead class="table-dark">
              <tr>
                <th scope="col"></th>
                <th scope="col" v-for="subject in filteredStatistics.subjects" :key="subject">{{ subject }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="row" class="text-muted">Class Average</th>
                <td v-for="(avg, index) in filteredStatistics.classAverage" :key="index">{{ avg }}</td>
              </tr>
              <tr>
                <th scope="row" class="text-muted">Reached Norm</th>
                <td v-for="(norm, index) in filteredStatistics.reachedNorm" :key="index">
                  <span :class="norm === 'yes' ? 'badge bg-success' : 'badge bg-danger'">{{ norm }}</span>
                </td>
              </tr>
              <tr>
                <th scope="row" class="text-muted">Passing %</th>
                <td v-for="(perc, index) in filteredStatistics.passingPerc" :key="index">{{ perc }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div v-if="!selectedClass" class="text-center text-muted py-4">
          <p class="mb-0">Please select a class first to view statistics.</p>
        </div>

        <div v-else-if="selectedClass && filteredStatistics.subjects.length === 0" class="text-center text-muted py-4">
          <p class="mb-0">No statistics data available for {{ selectedClass.name }} yet.</p>
        </div>
      </div>

      <!-- Default Content -->
      <div v-else class="default-content">
        <slot>
          <div class="placeholder-content">
            <p class="placeholder-text">Add your content here</p>
          </div>
        </slot>
      </div>

    </div>
  </div>
</template>

<style scoped>
.class-item:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Custom violet background for headers */
.bg-violet {
  background-color: #E8436E !important;
}

/* Custom purple buttons */
.btn-purple {
  background-color: #872650;
  border-color: #872650;
  color: white;
}




</style>
