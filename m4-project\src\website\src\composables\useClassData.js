import { ref, reactive, computed, inject } from 'vue'

export function useClassData() {
  // Inject global state
  const selectedClass = inject('selectedClass')
  const selectClass = inject('selectClass')

  // Reactive data arrays
  const classesData = ref([])
  const studentsData = ref([])
  const documentsData = ref([])

  // Statistics data
  const statisticsData = reactive({
    subjects: ['math', 'english', 'science'],
    classAverage: [8.2, 7.5, 8.8],
    reachedNorm: ['yes', 'no', 'yes'],
    passingPerc: ['85%', '72%', '91%']
  })

  // Computed properties for filtered data
  const filteredStudents = computed(() => {
    if (!selectedClass.value) return []
    return studentsData.value.filter(student => student.classId === selectedClass.value.id)
  })

  const filteredDocuments = computed(() => {
    if (!selectedClass.value) return []
    return documentsData.value.filter(document => document.classId === selectedClass.value.id)
  })

  const filteredStatistics = computed(() => {
    if (!selectedClass.value) return { subjects: [], classAverage: [], reachedNorm: [], passingPerc: [] }
    return statisticsData
  })

  // CRUD operations
  const addClass = (classData) => {
    const newClass = {
      id: Date.now(),
      name: classData.name,
      teacher: `${classData.teacherFirstName} ${classData.teacherLastName}`
    }
    classesData.value.push(newClass)
    return newClass
  }

  const addStudent = (studentName) => {
    if (!selectedClass.value) return null
    const newStudent = {
      id: Date.now(),
      name: studentName.trim(),
      classId: selectedClass.value.id
    }
    studentsData.value.push(newStudent)
    return newStudent
  }

  const addDocument = (documentData) => {
    if (!selectedClass.value) return null
    const newDocument = {
      id: Date.now(),
      title: documentData.title,
      lastEdited: new Date().toLocaleDateString('en-GB'),
      editor: documentData.editor,
      classId: selectedClass.value.id
    }
    documentsData.value.push(newDocument)
    return newDocument
  }

  const handleClassSelect = (classItem) => {
    selectClass(classItem)
  }

  return {
    // Data
    classesData,
    studentsData,
    documentsData,
    statisticsData,
    selectedClass,
    
    // Computed
    filteredStudents,
    filteredDocuments,
    filteredStatistics,
    
    // Methods
    addClass,
    addStudent,
    addDocument,
    handleClassSelect
  }
}
